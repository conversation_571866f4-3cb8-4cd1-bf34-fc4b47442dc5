"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { signIn } from "@/lib/auth/client";
import { useQueryState, parseAsString } from "nuqs";
import { useTranslations } from "next-intl";

const loginSchema = z.object({
	email: z.string().email({
		message: "Please enter a valid email address.",
	}),
	password: z.string().min(1, {
		message: "Password is required.",
	}),
});

type LoginValues = z.infer<typeof loginSchema>;

export function LoginForm() {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const { toast } = useToast();
	const [callbackUrl] = useQueryState(
		"callbackUrl",
		parseAsString.withDefault("/")
	);
	const t = useTranslations("forms.login");

	const form = useForm<LoginValues>({
		resolver: zodResolver(loginSchema),
		defaultValues: {
			email: "",
			password: "",
		},
	});

	async function onSubmit(values: LoginValues) {
		setIsSubmitting(true);

		try {
			const { error } = await signIn.email({
				email: values.email,
				password: values.password,
				callbackURL: callbackUrl,
				fetchOptions: {
					onSuccess: () => {
						toast({
							title: t("successTitle"),
							description: t("successMessage"),
						});
					},
				},
			});

			if (error) {
				toast({
					title: t("errorTitle"),
					description: error.message || t("invalidCredentials"),
					variant: "destructive",
				});
			}
		} catch (error) {
			toast({
				title: t("errorTitle"),
				description: t("errorMessage"),
				variant: "destructive",
			});
		} finally {
			setIsSubmitting(false);
		}
	}

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
				<FormField
					control={form.control}
					name="email"
					render={({ field }) => (
						<FormItem>
							<FormLabel>{t("email")}</FormLabel>
							<FormControl>
								<Input
									type="email"
									placeholder="<EMAIL>"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="password"
					render={({ field }) => (
						<FormItem>
							<FormLabel>{t("password")}</FormLabel>
							<FormControl>
								<Input
									type="password"
									placeholder="********"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<Button
					type="submit"
					className="w-full"
					disabled={isSubmitting}
				>
					{isSubmitting ? t("submitting") : t("submit")}
				</Button>

				<div className="relative">
					<div className="absolute inset-0 flex items-center">
						<span className="w-full border-t" />
					</div>
					<div className="relative flex justify-center text-xs uppercase">
						<span className="bg-background px-2 text-muted-foreground">
							{t("orContinueWith")}
						</span>
					</div>
				</div>

				<div className="grid grid-cols-2 gap-4">
					<Button
						variant="outline"
						type="button"
						disabled={isSubmitting}
						onClick={async () => {
							try {
								await signIn.social({
									provider: "github",
									callbackURL: callbackUrl,
								});
							} catch (error) {
								toast({
									title: t("errorTitle"),
									description: t("githubError"),
									variant: "destructive",
								});
							}
						}}
					>
						GitHub
					</Button>
					<Button
						variant="outline"
						type="button"
						disabled={isSubmitting}
						onClick={async () => {
							try {
								await signIn.social({
									provider: "google",
									callbackURL: callbackUrl,
								});
							} catch (error) {
								toast({
									title: t("errorTitle"),
									description: t("googleError"),
									variant: "destructive",
								});
							}
						}}
					>
						Google
					</Button>
				</div>
			</form>
		</Form>
	);
}
